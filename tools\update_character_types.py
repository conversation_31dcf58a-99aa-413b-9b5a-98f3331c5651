#!/usr/bin/env python3
"""
Script to update characters.json config file based on actual image file locations.

This script:
1. Scans the "assets" folder and creates a dict to track the actual file path and type for each image
2. Reads the config file and compares the items with the dict created previously
3. Updates the config file with correct types based on actual file locations
4. Shows a summary of changes made
"""

import json
import os
import glob
from pathlib import Path
from collections import defaultdict

def scan_assets_folder(assets_path="assets"):
    """
    Scan the assets folder and create a mapping of filename to actual type and path.
    
    Returns:
        dict: {filename: {"type": actual_type, "path": relative_path}}
    """
    file_mapping = {}
    
    if not os.path.exists(assets_path):
        print(f"Error: Assets folder '{assets_path}' not found!")
        return file_mapping
    
    # Get all subdirectories in assets folder (these are the types)
    type_folders = [d for d in os.listdir(assets_path) 
                   if os.path.isdir(os.path.join(assets_path, d))]
    
    print(f"Found type folders: {type_folders}")
    
    for type_folder in type_folders:
        type_path = os.path.join(assets_path, type_folder)
        
        # Find all image files in this type folder
        image_extensions = ['*.webp', '*.png', '*.jpg', '*.jpeg']
        for ext in image_extensions:
            pattern = os.path.join(type_path, ext)
            for file_path in glob.glob(pattern):
                filename = os.path.basename(file_path)
                relative_path = os.path.relpath(file_path).replace('\\', '/')
                
                file_mapping[filename] = {
                    "type": type_folder,
                    "path": relative_path
                }
    
    print(f"Found {len(file_mapping)} image files across all type folders")
    return file_mapping

def load_config_file(config_path="characters.json"):
    """Load the characters.json config file."""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"Error: Config file '{config_path}' not found!")
        return []
    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON in config file: {e}")
        return []

def update_config_with_actual_types(config_data, file_mapping):
    """
    Update config entries with actual types based on file locations.
    
    Returns:
        tuple: (updated_config, changes_summary)
    """
    changes = {
        "updated": [],
        "missing_files": [],
        "path_updates": []
    }
    
    for entry in config_data:
        if "image_path" not in entry:
            continue
            
        current_path = entry["image_path"]
        filename = os.path.basename(current_path)
        
        if filename in file_mapping:
            actual_info = file_mapping[filename]
            actual_type = actual_info["type"]
            actual_path = actual_info["path"]
            
            # Check if type needs updating
            if entry.get("type") != actual_type:
                old_type = entry.get("type", "unknown")
                entry["type"] = actual_type
                changes["updated"].append({
                    "filename": filename,
                    "old_type": old_type,
                    "new_type": actual_type,
                    "prompt": entry.get("prompt", "unknown")
                })
            
            # Check if path needs updating
            if current_path != actual_path:
                entry["image_path"] = actual_path
                changes["path_updates"].append({
                    "filename": filename,
                    "old_path": current_path,
                    "new_path": actual_path
                })
        else:
            changes["missing_files"].append({
                "filename": filename,
                "current_path": current_path,
                "prompt": entry.get("prompt", "unknown")
            })
    
    return config_data, changes

def save_config_file(config_data, config_path="characters.json"):
    """Save the updated config file."""
    try:
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"Error saving config file: {e}")
        return False

def print_summary(changes):
    """Print a summary of changes made."""
    print("\n" + "="*60)
    print("SUMMARY OF CHANGES")
    print("="*60)
    
    # Type updates
    if changes["updated"]:
        print(f"\n✅ TYPE UPDATES ({len(changes['updated'])} files):")
        type_change_counts = defaultdict(lambda: defaultdict(int))
        
        for change in changes["updated"]:
            old_type = change["old_type"]
            new_type = change["new_type"]
            type_change_counts[old_type][new_type] += 1
            print(f"  • {change['filename']}")
            print(f"    {old_type} → {new_type}")
            print(f"    Prompt: {change['prompt'][:50]}...")
        
        print(f"\n📊 TYPE CHANGE SUMMARY:")
        for old_type, new_types in type_change_counts.items():
            for new_type, count in new_types.items():
                print(f"  {old_type} → {new_type}: {count} files")
    else:
        print("\n✅ No type updates needed - all types are correct!")
    
    # Path updates
    if changes["path_updates"]:
        print(f"\n🔄 PATH UPDATES ({len(changes['path_updates'])} files):")
        for change in changes["path_updates"]:
            print(f"  • {change['filename']}")
            print(f"    {change['old_path']} → {change['new_path']}")
    else:
        print("\n✅ No path updates needed - all paths are correct!")
    
    # Missing files
    if changes["missing_files"]:
        print(f"\n❌ MISSING FILES ({len(changes['missing_files'])} files):")
        for missing in changes["missing_files"]:
            print(f"  • {missing['filename']}")
            print(f"    Expected path: {missing['current_path']}")
            print(f"    Prompt: {missing['prompt'][:50]}...")
    else:
        print("\n✅ No missing files - all images found!")
    
    print(f"\n📈 TOTAL STATISTICS:")
    print(f"  • Type updates: {len(changes['updated'])}")
    print(f"  • Path updates: {len(changes['path_updates'])}")
    print(f"  • Missing files: {len(changes['missing_files'])}")

def main():
    """Main function to run the update process."""
    print("Starting character type update process...")
    
    # Step 1: Scan assets folder
    print("\n1. Scanning assets folder...")
    file_mapping = scan_assets_folder()
    
    if not file_mapping:
        print("No files found in assets folder. Exiting.")
        return
    
    # Step 2: Load config file
    print("\n2. Loading config file...")
    config_data = load_config_file()
    
    if not config_data:
        print("No config data loaded. Exiting.")
        return
    
    print(f"Loaded {len(config_data)} entries from config file")
    
    # Step 3: Update config with actual types
    print("\n3. Comparing config with actual file locations...")
    updated_config, changes = update_config_with_actual_types(config_data, file_mapping)
    
    # Step 4: Save updated config
    if changes["updated"] or changes["path_updates"]:
        print("\n4. Saving updated config file...")
        if save_config_file(updated_config):
            print("✅ Config file updated successfully!")
        else:
            print("❌ Failed to save config file!")
            return
    else:
        print("\n4. No changes needed - config file is already up to date!")
    
    # Step 5: Show summary
    print_summary(changes)

if __name__ == "__main__":
    main()
